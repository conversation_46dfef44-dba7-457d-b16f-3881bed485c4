import { Tabs } from "expo-router";
import React from "react";
import { Camera, Leaf, Search, User, Stethoscope } from "lucide-react-native";
import { Colors } from "@/constants/colors";
import { GardenProvider } from "@/hooks/useGardenStore";
import { FavoritesProvider } from "@/hooks/useFavoritesStore";
import { IdentificationProvider } from "@/hooks/useIdentificationStore";

export default function TabLayout() {
  return (
    <IdentificationProvider>
      <GardenProvider>
        <FavoritesProvider>
          <Tabs
            screenOptions={{
              tabBarActiveTintColor: Colors.primary,
              tabBarInactiveTintColor: Colors.textMuted,
              tabBarStyle: {
                borderTopWidth: 1,
                borderTopColor: Colors.border,
              },
              headerStyle: {
                backgroundColor: Colors.background,
              },
              headerTitleStyle: {
                color: Colors.text,
                fontWeight: '600',
              },
              tabBarLabelStyle: {
                fontSize: 12,
              },
            }}
          >
            <Tabs.Screen
              name="index"
              options={{
                title: "Snap",
                tabBarIcon: ({ color }) => <Camera size={24} color={color} />,
              }}
            />
            <Tabs.Screen
              name="diagnose"
              options={{
                title: "Diagnose",
                tabBarIcon: ({ color }) => <Stethoscope size={24} color={color} />,
              }}
            />
            <Tabs.Screen
              name="garden"
              options={{
                title: "Garden",
                tabBarIcon: ({ color }) => <Leaf size={24} color={color} />,
              }}
            />
            <Tabs.Screen
              name="discover"
              options={{
                title: "Discover",
                tabBarIcon: ({ color }) => <Search size={24} color={color} />,
              }}
            />
            <Tabs.Screen
              name="profile"
              options={{
                title: "Profile",
                tabBarIcon: ({ color }) => <User size={24} color={color} />,
              }}
            />
          </Tabs>
        </FavoritesProvider>
      </GardenProvider>
    </IdentificationProvider>
  );
}