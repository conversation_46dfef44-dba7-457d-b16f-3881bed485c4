import React, { useState } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity, ScrollView, TextInput } from 'react-native';
import { Stack } from 'expo-router';
import { Stethoscope, Leaf, Info } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { Colors } from '@/constants/colors';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { BloomSnapCamera } from '@/components/camera/CameraView';
import { EnhancedIdentificationResultView } from '@/components/identification/EnhancedIdentificationResult';
import { ProgressIndicator } from '@/components/ui/ProgressIndicator';
import { useIdentification } from '@/hooks/useIdentificationStore';
import { useGarden } from '@/hooks/useGardenStore';
import { Platform } from 'react-native';

export default function DiagnoseScreen() {
  const [showCamera, setShowCamera] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [problemDescription, setProblemDescription] = useState('');
  const { identifyPlant, currentResult, clearCurrentResult, isIdentifying } = useIdentification();
  const { addPlant } = useGarden();

  const handleCapture = async (uri: string) => {
    setShowCamera(false);
    setCapturedImage(uri);
    setShowPreview(true);
  };

  const handleDiagnose = async () => {
    if (capturedImage) {
      setShowPreview(false);
      await identifyPlant(capturedImage, problemDescription);
    }
  };

  const handleAddToGarden = () => {
    if (currentResult) {
      addPlant(currentResult.plant);
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    }
  };

  const handleNewScan = () => {
    clearCurrentResult();
    setCapturedImage(null);
    setProblemDescription('');
    setShowCamera(true);
  };

  const startCamera = () => {
    clearCurrentResult();
    setCapturedImage(null);
    setShowCamera(true);
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  };

  const handleRetakePhoto = () => {
    setCapturedImage(null);
    setShowPreview(false);
    setShowCamera(true);
  };

  if (showCamera) {
    return <BloomSnapCamera onCapture={handleCapture} onCancel={() => setShowCamera(false)} mode="diagnose" />;
  }

  if (isIdentifying) {
    return <ProgressIndicator mode="diagnose" />;
  }

  if (showPreview && capturedImage) {
    return (
      <View style={styles.container} testID="preview-screen">
        <Stack.Screen options={{ title: 'Plant Diagnosis Preview' }} />
        
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.previewContainer}>
            <Image source={{ uri: capturedImage }} style={styles.previewImage} />
            
            <View style={styles.previewContent}>
              <Text style={styles.previewTitle}>Describe the Problem</Text>
              <Text style={styles.previewSubtitle}>
                Help our AI diagnose your plant by describing what you&apos;ve noticed
              </Text>
              
              <View style={styles.problemInputContainer}>
                <Text style={styles.problemInputLabel}>What&apos;s wrong with your plant? (optional)</Text>
                <TextInput
                  style={styles.problemInput}
                  placeholder="e.g., mushy leaves, brown spots, wilting, yellowing..."
                  placeholderTextColor={Colors.textMuted}
                  value={problemDescription}
                  onChangeText={setProblemDescription}
                  multiline
                  numberOfLines={4}
                  testID="problem-description-input"
                />
              </View>
              
              <View style={styles.previewActions}>
                <Button
                  title="Retake Photo"
                  variant="outline"
                  onPress={handleRetakePhoto}
                  style={styles.retakeButton}
                  testID="retake-button"
                />
                <Button
                  title="Diagnose Plant"
                  onPress={handleDiagnose}
                  style={styles.diagnoseButton}
                  loading={isIdentifying}
                  testID="diagnose-button"
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }

  if (currentResult) {
    return (
      <EnhancedIdentificationResultView
        result={currentResult}
        onAddToGarden={handleAddToGarden}
        onNewScan={handleNewScan}
      />
    );
  }

  return (
    <View style={styles.container} testID="diagnose-screen">
      <Stack.Screen options={{ title: 'Plant Doctor' }} />
      
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Image
            source={{ uri: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80' }}
            style={styles.headerImage}
          />
          <View style={styles.headerContent}>
            <Text style={styles.title}>Diagnose plant health issues</Text>
            <Text style={styles.subtitle}>
              Take a photo of your plant and get expert diagnosis with treatment recommendations
            </Text>
          </View>
        </View>

        <View style={styles.actionsContainer}>
          <Button
            title="Start Plant Diagnosis"
            onPress={startCamera}
            style={styles.snapButton}
            textStyle={styles.snapButtonText}
            testID="start-camera-button"
          />
          
          <View style={styles.featureCards}>
            <Card style={styles.featureCard}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.secondary }]}>
                <Stethoscope size={24} color={Colors.primaryDark} />
              </View>
              <Text style={styles.featureTitle}>AI Diagnosis</Text>
              <Text style={styles.featureText}>Advanced AI analyzes plant health</Text>
            </Card>
            
            <Card style={styles.featureCard}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.tertiary }]}>
                <Leaf size={24} color={Colors.primaryDark} />
              </View>
              <Text style={styles.featureTitle}>Treatment Plans</Text>
              <Text style={styles.featureText}>Get personalized care recommendations</Text>
            </Card>
            
            <Card style={styles.featureCard}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.accent1 }]}>
                <Info size={24} color={Colors.primaryDark} />
              </View>
              <Text style={styles.featureTitle}>Expert Tips</Text>
              <Text style={styles.featureText}>Learn from plant care experts</Text>
            </Card>
          </View>
        </View>

        <View style={styles.recentContainer}>
          <Text style={styles.sectionTitle}>Common Plant Issues</Text>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.recentScroll}>
            {[
              { id: '1', name: 'Yellowing Leaves', image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.0.3&w=400&q=80' },
              { id: '2', name: 'Brown Spots', image: 'https://images.unsplash.com/photo-1463320726281-696a485928c7?ixlib=rb-4.0.3&w=400&q=80' },
              { id: '3', name: 'Wilting', image: 'https://images.unsplash.com/photo-1518335935020-cfd9c7d28e8a?ixlib=rb-4.0.3&w=400&q=80' },
              { id: '4', name: 'Pest Damage', image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.0.3&w=400&q=80' },
            ].map((issue) => (
              <TouchableOpacity key={issue.id} style={styles.recentItem} testID={`common-issue-${issue.id}`}>
                <Image source={{ uri: issue.image }} style={styles.recentImage} />
                <Text style={styles.recentName}>{issue.name}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    position: 'relative',
    height: 200,
    marginBottom: 20,
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  headerContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.background,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.background,
    opacity: 0.9,
  },
  actionsContainer: {
    padding: 20,
  },
  snapButton: {
    height: 60,
    borderRadius: 30,
    marginBottom: 24,
  },
  snapButtonText: {
    fontSize: 18,
  },
  featureCards: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  featureCard: {
    flex: 1,
    marginHorizontal: 4,
    padding: 12,
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  featureText: {
    fontSize: 12,
    color: Colors.textLight,
    textAlign: 'center',
  },
  recentContainer: {
    padding: 20,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  recentScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  recentItem: {
    width: 120,
    marginRight: 16,
  },
  recentImage: {
    width: 120,
    height: 120,
    borderRadius: 12,
    marginBottom: 8,
  },
  recentName: {
    fontSize: 14,
    color: Colors.text,
    textAlign: 'center',
  },
  previewContainer: {
    flex: 1,
  },
  previewImage: {
    width: '100%',
    height: 300,
    resizeMode: 'cover',
  },
  previewContent: {
    padding: 20,
  },
  previewTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 8,
  },
  previewSubtitle: {
    fontSize: 16,
    color: Colors.textMuted,
    marginBottom: 24,
  },
  problemInputContainer: {
    marginBottom: 24,
  },
  problemInputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 8,
  },
  problemInput: {
    backgroundColor: Colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.text,
    textAlignVertical: 'top',
    minHeight: 100,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  previewActions: {
    flexDirection: 'row',
    gap: 12,
  },
  retakeButton: {
    flex: 1,
  },
  diagnoseButton: {
    flex: 2,
  },
});