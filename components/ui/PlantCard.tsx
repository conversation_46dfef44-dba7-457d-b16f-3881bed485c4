import React from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity } from 'react-native';
import { Heart } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Plant } from '@/types/plant';
import { Card } from './Card';

interface PlantCardProps {
  plant: Plant;
  onPress?: () => void;
  onFavorite?: () => void;
  isFavorite?: boolean;
  showFavorite?: boolean;
}

export const PlantCard: React.FC<PlantCardProps> = ({
  plant,
  onPress,
  onFavorite,
  isFavorite = false,
  showFavorite = true,
}) => {
  return (
    <Card onPress={onPress} style={styles.container} testID={`plant-card-${plant.id}`}>
      <Image source={{ uri: plant.imageUrl }} style={styles.image} />
      <View style={styles.content}>
        <View style={styles.header}>
          <View>
            <Text style={styles.commonName}>{plant.commonName}</Text>
            <Text style={styles.scientificName}>{plant.scientificName}</Text>
          </View>
          {showFavorite && (
            <TouchableOpacity onPress={onFavorite} style={styles.favoriteButton}>
              <Heart 
                size={24} 
                color={isFavorite ? Colors.error : Colors.textMuted} 
                fill={isFavorite ? Colors.error : 'transparent'} 
              />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.tags}>
          {plant.tags.slice(0, 3).map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 0,
    overflow: 'hidden',
    width: '100%',
  },
  image: {
    width: '100%',
    height: 180,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  commonName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 14,
    fontStyle: 'italic',
    color: Colors.textLight,
    marginBottom: 8,
  },
  favoriteButton: {
    padding: 4,
  },
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  tag: {
    backgroundColor: Colors.secondary,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: Colors.primaryDark,
  },
});