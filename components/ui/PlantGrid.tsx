import React from 'react';
import { StyleSheet, FlatList, ListRenderItem, View, Dimensions } from 'react-native';
import { Plant } from '@/types/plant';
import { PlantCard } from './PlantCard';

interface PlantGridProps {
  plants: Plant[];
  onPlantPress: (plant: Plant) => void;
  onFavoritePress?: (plant: Plant) => void;
  favorites?: string[];
  numColumns?: number;
  showFavorites?: boolean;
}

export const PlantGrid: React.FC<PlantGridProps> = ({
  plants,
  onPlantPress,
  onFavoritePress,
  favorites = [],
  numColumns = 1,
  showFavorites = true,
}) => {
  const renderItem: ListRenderItem<Plant> = ({ item }) => (
    <View style={[styles.item, { width: `${100 / numColumns}%` }]}>
      <PlantCard
        plant={item}
        onPress={() => onPlantPress(item)}
        onFavorite={() => onFavoritePress && onFavoritePress(item)}
        isFavorite={favorites.includes(item.id)}
        showFavorite={showFavorites}
      />
    </View>
  );

  return (
    <FlatList
      data={plants}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      numColumns={numColumns}
      contentContainerStyle={styles.container}
      showsVerticalScrollIndicator={false}
      testID="plant-grid"
    />
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  item: {
    paddingHorizontal: 8,
  },
});