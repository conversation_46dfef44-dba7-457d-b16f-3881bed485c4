import { useState } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import { Plant, IdentificationResult } from '@/types/plant';
import { OpenRouterService, PlantIdentificationData, DiagnosisData } from '@/services/openrouter';

export const [IdentificationProvider, useIdentification] = createContextHook(() => {
  const [results, setResults] = useState<IdentificationResult[]>([]);
  const [currentResult, setCurrentResult] = useState<IdentificationResult | null>(null);
  const [isIdentifying, setIsIdentifying] = useState(false);

  // Plant identification using OpenRouter AI
  const identifyPlant = async (imageUri: string, problemDescription?: string): Promise<IdentificationResult> => {
    setIsIdentifying(true);

    try {
      // Use diagnosis mode if problem description is provided
      const identificationData = problemDescription
        ? await OpenRouterService.diagnosePlant(imageUri, problemDescription)
        : await OpenRouterService.identifyPlant(imageUri);

      // Convert OpenRouter response to Plant format
      const plant: Plant = {
        id: `plant_${Date.now()}`, // Generate unique ID
        scientificName: identificationData.scientificName,
        commonName: identificationData.commonName,
        imageUrl: imageUri, // Use the captured image
        description: identificationData.description,
        careInstructions: identificationData.careInstructions,
        tags: identificationData.tags,
      };

      const result: IdentificationResult = {
        plant,
        confidence: identificationData.confidence,
        timestamp: new Date(),
        imageUri,
        problemDescription,
        // Store the full identification data for enhanced display
        identificationData,
        // Add diagnosis info if available (for diagnosis mode)
        ...(problemDescription && 'diagnosedProblem' in identificationData && {
          diagnosisData: identificationData as DiagnosisData
        }),
      };

      setResults(prev => [result, ...prev]);
      setCurrentResult(result);

      return result;
    } catch (error) {
      console.error('Plant identification failed:', error);
      // You might want to show an error message to the user here
      throw error;
    } finally {
      setIsIdentifying(false);
    }
  };

  const clearCurrentResult = () => {
    setCurrentResult(null);
  };

  return {
    results,
    currentResult,
    isIdentifying,
    identifyPlant,
    clearCurrentResult,
  };
});